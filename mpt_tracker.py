from app import app
import MetaTrader5 as mt5
from dash import Output, Input, State, html
import dash
import plotly.graph_objects as go
import numpy as np
import scipy.optimize as sco
import pandas as pd
from plotly.subplots import make_subplots
from datetime import datetime, timedelta
import pytz

from func_gfx import build_weights_table, create_combined_mpt_string
from func_mt5 import fetch_data, calculate_returns, convert_log_to_arithmetic_returns
from func_portfolio import process_custom_portfolio
from ratio_calcs import portfolio_variance, neg_sharpe_ratio, neg_sortino_ratio, compute_omega_ratio, compute_calmar_ratio, neg_modified_sharpe_ratio
from weekend_utils import should_freeze_updates, should_use_friday_data, cache_friday_data, get_cached_friday_data, log_weekend_status

def create_m15_chart_with_lr_channels(portfolio_weights, symbols):
    """
    Create a chart showing 7 trading days of M15 portfolio data with multiple linear regression channels:
    - 480 periods (white)
    - 240 periods (blue)
    - 120 periods (red)
    - 72 periods (yellow)

    Args:
        portfolio_weights: Dict mapping symbol names to weights (e.g., {'EURUSD': 0.5, 'GBPUSD': 0.3})
        symbols: List of symbol names to fetch data for
    """
    try:
        # Calculate enough calendar days to get 7 trading days (fetch 9 calendar days)
        now_local = datetime.now(pytz.timezone('Europe/Bucharest'))
        nine_days_ago = now_local - timedelta(days=9)

        print(f"Fetching 9 calendar days of M15 data from {nine_days_ago} to {now_local} to get 7 trading days")

        # Fetch 9 calendar days of M15 data to ensure we get 7 trading days
        market_data = fetch_data(symbols, timeframe=mt5.TIMEFRAME_M15, shift=0,
                               start_time=nine_days_ago, end_time=now_local)

        if market_data is None or len(market_data) == 0:
            print("No M15 data available for 7-trading-day chart")
            return go.Figure(layout=go.Layout(
                title="Portfolio 7-Trading-Day M15 Data - No Data Available",
                paper_bgcolor='black',
                plot_bgcolor='black',
                font=dict(color='white')
            ))

        # Calculate returns
        returns_df = calculate_returns(market_data)

        if returns_df.empty:
            print("No returns data available for 7-trading-day chart")
            return go.Figure(layout=go.Layout(
                title="Portfolio 7-Trading-Day M15 Data - No Returns Data",
                paper_bgcolor='black',
                plot_bgcolor='black',
                font=dict(color='white')
            ))

        # Calculate portfolio cumulative returns
        portfolio_returns = returns_df[list(portfolio_weights.keys())].fillna(0)
        weights = np.array([portfolio_weights[symbol] for symbol in portfolio_returns.columns])
        portfolio_cum_returns = (portfolio_returns @ weights).cumsum()

        # Filter out weekends (Saturday=5, Sunday=6)
        weekday_mask = portfolio_cum_returns.index.weekday < 5  # Monday=0 to Friday=4
        portfolio_cum_returns = portfolio_cum_returns[weekday_mask]

        # Keep only the last 7 trading days worth of data
        # Group by date and get the last 7 trading days
        trading_days_data = portfolio_cum_returns.groupby(portfolio_cum_returns.index.date).size()
        if len(trading_days_data) > 7:
            # Get the last 7 trading days
            last_7_days = trading_days_data.tail(7).index
            # Filter data to keep only the last 7 trading days
            portfolio_cum_returns = portfolio_cum_returns[
                portfolio_cum_returns.index.to_series().dt.date.isin(last_7_days)
            ]

        print(f"M15 data points after weekend filtering and 7-day limit: {len(portfolio_cum_returns)}")
        print(f"Trading days included: {len(portfolio_cum_returns.groupby(portfolio_cum_returns.index.date))}")

        # Create the figure
        fig = go.Figure()

        # Add portfolio trace
        fig.add_trace(go.Scatter(
            x=portfolio_cum_returns.index,
            y=portfolio_cum_returns,
            mode='lines',
            name="Portfolio M15",
            line=dict(color='lightblue', width=1)
        ))

        # Add multiple linear regression channels
        lr_configs = [
            {'periods': 480, 'color': 'white', 'name': '480p'},
            {'periods': 240, 'color': 'blue', 'name': '240p'},
            {'periods': 120, 'color': 'red', 'name': '120p'},
            {'periods': 72, 'color': 'yellow', 'name': '72p'}
        ]

        for config in lr_configs:
            add_lr_channel_to_fig(fig, portfolio_cum_returns, config)

        # Update layout
        fig.update_layout(
            title="Portfolio 7-Trading-Day M15 Data with Linear Regression Channels",
            yaxis_title="Cumulative Returns",
            paper_bgcolor='black',
            plot_bgcolor='black',
            font=dict(color='white'),
            height=800,
            hovermode="x unified",
            legend=dict(
                x=0.05,
                y=0.95,
                xanchor='left',
                yanchor='top'
            ),
            xaxis=dict(
                rangebreaks=[
                    dict(bounds=["sat", "mon"], pattern="day of week")  # Hide weekends
                ]
            )
        )

        return fig

    except Exception as e:
        print(f"Error creating M15 chart: {e}")
        return go.Figure(layout=go.Layout(
            title=f"Portfolio 7-Trading-Day M15 Data - Error: {str(e)}",
            paper_bgcolor='black',
            plot_bgcolor='black',
            font=dict(color='white')
        ))

def add_lr_channel_to_fig(fig, data, config):
    """Add a linear regression channel to the figure"""
    try:
        periods = config['periods']
        color = config['color']
        name = config['name']

        # Get the last N periods
        if len(data) < periods:
            print(f"Not enough data for {periods} period LR channel: {len(data)} available")
            return

        lr_data = data.iloc[-periods:].dropna()
        if len(lr_data) < 10:  # Minimum data requirement
            print(f"Insufficient data for {periods} period LR channel: {len(lr_data)} points")
            return

        # Calculate linear regression
        x = np.arange(len(lr_data))
        y = lr_data.values
        slope, intercept = np.polyfit(x, y, 1)
        reg_line = slope * x + intercept

        # Calculate channel bounds using 2 standard deviations
        residuals = y - reg_line
        std_dev = np.std(residuals)
        channel_offset = 2 * std_dev
        lower_channel = reg_line - channel_offset
        upper_channel = reg_line + channel_offset

        # Add mid line trace (skip for 72 period channel)
        if periods != 72 and periods != 120:
            fig.add_trace(go.Scatter(
                x=lr_data.index,
                y=reg_line,
                mode='lines',
                name=f'LR {name}',
                line=dict(color=color, width=3)
            ))

        fig.add_trace(go.Scatter(
            x=lr_data.index,
            y=lower_channel,
            mode='lines',
            name=f'LR {name} Lower',
            line=dict(color=color, width=2)
        ))

        fig.add_trace(go.Scatter(
            x=lr_data.index,
            y=upper_channel,
            mode='lines',
            name=f'LR {name} Upper',
            line=dict(color=color, width=2)
        ))

        print(f"Added {periods} period LR channel in {color}")

    except Exception as e:
        print(f"Error adding {config['periods']} period LR channel: {e}")

@app.callback(
    [Output('mpt-weights-table', 'children'),
     Output('mpt-tracker', 'figure'),
     Output('mpt-weights-store', 'data'),
     Output('mpt-tracker-weights', 'value'),
     Output('portfolio-m15-chart', 'figure')],
    [Input('update-mpt', 'n_clicks'),
     Input('interval-component1', 'n_intervals'),
     Input('mpt-size', 'value')],
    [State('portfolio-allocation', 'value')]
)
def update_mpt_tracker(n_clicks, n_intervals, annotation_scale, allocation_string):
    # Log weekend status for debugging
    log_weekend_status()

    # Check if we should use Friday data during weekend
    cache_key = f"mpt_tracker_{allocation_string}_{annotation_scale}"

    # Add debug logging for weekend detection
    weekend_status = should_use_friday_data()
    print(f"Weekend detection result: {weekend_status}")

    if weekend_status:
        # Try to get cached Friday data
        cached_data = get_cached_friday_data(cache_key)
        if cached_data is not None:
            print("Weekend mode: Using cached Friday MPT tracker data")
            # Handle both old 4-tuple and new 5-tuple cached data formats
            if len(cached_data) == 4:
                # Old format: add empty M15 chart
                weights_table, fig, store_data, combined_mpt_string = cached_data
                empty_m15_fig = go.Figure(layout=go.Layout(
                    title="Portfolio 6-Day M15 Data - Weekend Mode (Cached Data)",
                    paper_bgcolor='black',
                    plot_bgcolor='black',
                    font=dict(color='white')
                ))
                return weights_table, fig, store_data, combined_mpt_string, empty_m15_fig
            else:
                # New format: return as-is
                return cached_data
        else:
            print("Weekend mode: No cached data available, using Friday data with current parameters")
    else:
        print("Weekday mode: Processing live market data")

    # Do nothing until an allocation is provided.
    if not allocation_string:
        empty_m15_fig = go.Figure(layout=go.Layout(
            title="Portfolio 6-Day M15 Data - No Portfolio Selected",
            paper_bgcolor='black',
            plot_bgcolor='black',
            font=dict(color='white')
        ))
        return html.Div(), go.Figure(), {}, "", empty_m15_fig
    
    # Parse the allocation string.
    # Expected format: "EURUSD:0.4, AUDJPY.:-0.3, GBPUSD:0.3", where a trailing dot in a
    # symbol (e.g. "AUDJPY.") marks it as locked.
    locked_allocations = {}
    free_allocations = {}
    locked_symbols = set()
    
    try:
        for part in allocation_string.split(','):
            symbol_weight = part.split(':')
            if len(symbol_weight) != 2:
                continue
                
            symbol, weight_str = symbol_weight
            symbol = symbol.strip()
            weight = float(weight_str.strip())
            locked = False
            
            if symbol.endswith('.'):
                locked = True
                symbol = symbol[:-1]  # Remove trailing dot
                
            symbol = symbol.upper()  # Force uppercase
            
            if locked:
                locked_allocations[symbol] = weight
                locked_symbols.add(symbol)
            else:
                free_allocations[symbol] = weight
    except Exception as e:
        print(f"Error getting allocations: {e}")
        fig = go.Figure()
        fig.update_layout(
            title="Invalid allocation input. Expected format: EURUSD:0.4, AUDJPY.:-0.3, GBPUSD:0.3, ...",
            paper_bgcolor='black', 
            plot_bgcolor='black',
            font=dict(color='white')
        )
        empty_m15_fig = go.Figure(layout=go.Layout(
            title="Portfolio 6-Day M15 Data - Invalid Allocation",
            paper_bgcolor='black',
            plot_bgcolor='black',
            font=dict(color='white')
        ))
        return html.Div(), fig, {}, "", empty_m15_fig

    # Calculate absolute sums for normalization
    locked_abs_sum = sum(abs(w) for w in locked_allocations.values())
    free_abs_sum = sum(abs(w) for w in free_allocations.values())
    total_abs_sum = locked_abs_sum + free_abs_sum
    
    # If there are no allocations, create a default figure
    if total_abs_sum == 0:
        fig = go.Figure()
        fig.update_layout(
            title="No valid allocations found. Please input allocations.",
            paper_bgcolor='black', 
            plot_bgcolor='black',
            font=dict(color='white')
        )
        empty_m15_fig = go.Figure(layout=go.Layout(
            title="Portfolio 6-Day M15 Data - No Valid Allocations",
            paper_bgcolor='black',
            plot_bgcolor='black',
            font=dict(color='white')
        ))
        return html.Div(), fig, {}, "", empty_m15_fig
    
    # Normalize by absolute sum to ensure absolute weights sum to 1.0
    locked_allocations = {k: v/total_abs_sum for k, v in locked_allocations.items()}
    free_allocations = {k: v/total_abs_sum for k, v in free_allocations.items()}
    
    # Combine locked and free allocations
    allocations = {**locked_allocations, **free_allocations}
    
    # Verify the absolute sum is 1.0
    final_abs_sum = sum(abs(w) for w in allocations.values())
    if not np.isclose(final_abs_sum, 1.0):
        # Final normalization if needed due to floating point errors
        allocations = {k: v/final_abs_sum for k, v in allocations.items()}
    
    # Create a copy of allocations before applying the size multiplier
    # This will be used for portfolio construction
    normalized_allocations = allocations.copy()
    
    # Now apply the size multiplier (for display/annotation purposes)
    size_multiplier = annotation_scale if annotation_scale is not None else 0.80
    scaled_allocations = {k: v * size_multiplier for k, v in allocations.items()}
    
    # Set timezone and calculate hours since start of day
    local_tz = pytz.timezone('Europe/Bucharest')
    local_now = datetime.now(local_tz)
    midnight = local_now.replace(hour=0, minute=0, second=0, microsecond=0)
    hours = (local_now - midnight).total_seconds() / 3600  # Convert seconds to hours
    shift = 0

    symbols = list(allocations.keys())

    # Determine the correct timeframe for both chart and MPT calculations (Today or Previous Friday if weekend)
    # Use consistent timezone throughout
    now_local = datetime.now(local_tz)
    weekday = now_local.weekday()

    print(f"Current time: {now_local.strftime('%Y-%m-%d %H:%M:%S %Z')}, Weekday: {weekday}")

    if weekday < 5: # Monday to Friday (0-4)
        chart_start_time = now_local.replace(hour=0, minute=0, second=0, microsecond=0)
        chart_end_time = now_local  # Current time on weekdays
        mpt_start_time = chart_start_time
        mpt_end_time = chart_end_time
        print(f"Weekday mode: Using current day data from {chart_start_time} to {chart_end_time}")
    else: # Saturday (5) or Sunday (6)
        days_to_friday = (weekday - 4) % 7 # 1 for Sat, 2 for Sun
        last_friday_date = (now_local - timedelta(days=days_to_friday)).date()
        chart_start_time = local_tz.localize(datetime.combine(last_friday_date, datetime.min.time())) # Friday 00:00:00
        chart_end_time = local_tz.localize(datetime.combine(last_friday_date, datetime.max.time())).replace(microsecond=0) # Friday 23:59:59
        mpt_start_time = chart_start_time
        mpt_end_time = chart_end_time
        print(f"Weekend mode: Using Friday data from {chart_start_time} to {chart_end_time}")

    # Fetch data for the chart using explicit start/end times (00:00 to 23:59 only)
    market_data = fetch_data(symbols, timeframe=mt5.TIMEFRAME_M1, shift=shift, start_time=chart_start_time, end_time=chart_end_time)
    returns_df = calculate_returns(market_data) # Used for chart cumulative returns

    # Fetch data for MPT calculations using the same timeframe
    mpt_market_data = fetch_data(symbols, timeframe=mt5.TIMEFRAME_M1, shift=shift, start_time=mpt_start_time, end_time=mpt_end_time)
    mpt_returns_df = calculate_returns(mpt_market_data) # Used for MPT optimizations
    
    # Ensure symbols are available in BOTH dataframes for safety, prioritize MPT dataframe for availability check
    available_symbols_mpt = [sym for sym in allocations if sym in mpt_returns_df.columns]
    available_symbols = [sym for sym in available_symbols_mpt if sym in returns_df.columns] # Filter further based on 120h data

    if not available_symbols: # Check based on the intersection
        # If no symbols have data in *both* timeframes, return an error figure
        fig = go.Figure()

        # Create a more informative error message
        current_time = datetime.now(local_tz)
        if weekday < 5:  # Weekday
            error_msg = (f"No market data available for the given symbols in the required timeframes.<br>"
                        f"Current time: {current_time.strftime('%Y-%m-%d %H:%M:%S %Z')}<br>"
                        f"This may indicate the market is currently closed or data is not yet available.<br>"
                        f"The chart will update automatically when market data becomes available.")
        else:  # Weekend
            error_msg = (f"Weekend Mode: No market data available.<br>"
                        f"Showing Friday data when available.<br>"
                        f"Current time: {current_time.strftime('%Y-%m-%d %H:%M:%S %Z')}")

        fig.update_layout(
            title=error_msg,
            paper_bgcolor='black',
            plot_bgcolor='black',
            font=dict(color='white')
        )
        empty_m15_fig = go.Figure(layout=go.Layout(
            title="Portfolio 6-Day M15 Data - Data Error",
            paper_bgcolor='black',
            plot_bgcolor='black',
            font=dict(color='white')
        ))
        return html.Div(), fig, {}, "", empty_m15_fig

    # Compute covariance matrix and mean returns using TODAY'S data for MPT optimization.
    try:
        # Use mpt_returns_df for MPT calculations
        # Convert log returns to arithmetic returns for portfolio optimization
        mpt_arithmetic_returns = convert_log_to_arithmetic_returns(mpt_returns_df[available_symbols])
        mpt_cov_matrix = mpt_arithmetic_returns.cov()
        mpt_mean_returns = mpt_arithmetic_returns.mean()

        # Check for NaN values in MPT covariance matrix and mean returns
        if np.isnan(mpt_cov_matrix.values).any() or np.isnan(mpt_mean_returns.values).any():
            # Attempt to fill NaNs or drop symbols if necessary, or raise error
            # Simple approach: fill NaN cov with 0, NaN mean with 0
            mpt_cov_matrix.fillna(0, inplace=True)
            mpt_mean_returns.fillna(0, inplace=True)
            print("Warning: NaN values detected and filled in MPT covariance matrix or mean returns.")
            # Re-check after filling
            if np.isnan(mpt_cov_matrix.values).any() or np.isnan(mpt_mean_returns.values).any():
                 raise ValueError("NaN values persist after attempting to fill.")


        # Handle potential numerical instability by adding a small diagonal offset
        mpt_cov_matrix = mpt_cov_matrix + np.eye(len(mpt_cov_matrix)) * 1e-8
    except Exception as e:
        fig = go.Figure()
        fig.update_layout(
            title=f"Error computing portfolio statistics: {str(e)}",
            paper_bgcolor='black', 
            plot_bgcolor='black',
            font=dict(color='white')
        )
        empty_m15_fig = go.Figure(layout=go.Layout(
            title="Portfolio 6-Day M15 Data - Statistics Error",
            paper_bgcolor='black',
            plot_bgcolor='black',
            font=dict(color='white')
        ))
        return html.Div(), fig, {}, "", empty_m15_fig

    # Setup optimization parameters with updated bounds
    n = len(available_symbols)
    init_guess = np.repeat(1/n, n)
    # Standard constraint: sum(weights)=1 for all optimizations
    constraints = ({'type': 'eq', 'fun': lambda x: np.sum(x) - 1})
    bounds = tuple((-1, 1) for _ in range(n))  # Allow long and short positions
    
    # Initialize optimization result dictionaries
    minvar_weights = {}
    maxsharpe_weights = {}
    maxsortino_weights = {}
    maxomega_weights = {}
    maxcalmar_weights = {}
    modsharpe_weights = {}
    maxret_weights = {}
    
    # Run all optimizations with error handling for each one
    try:
        # --- Define Target Return Constraint ---
        target_return = 0.0 # Default
        min_ret_portfolio = 0.0
        max_ret_portfolio = 0.0
        if not mpt_mean_returns.empty:
             try:
                 min_ret_portfolio = np.dot(init_guess, mpt_mean_returns) # Return of equal-weight
                 max_ret_portfolio = mpt_mean_returns.max() # Return of best single asset
                 # Use midpoint as target return
                 target_return = (min_ret_portfolio + max_ret_portfolio) / 2.0
                 print(f"Calculated Target Return (Midpoint): {target_return:.8f}")
             except Exception as e:
                 print(f"Error calculating target return: {e}. Using default 0.0")
                 target_return = 0.0
        else:
             print("Warning: mpt_mean_returns is empty. Using default target return 0.0")

        # Define the portfolio return function for the constraint
        def portfolio_return_constraint(weights, mean_returns):
            return np.dot(weights, mean_returns)

        # Combine standard constraint with target return constraint (inequality >= target)
        constraints_minvar_target = [
            {'type': 'eq', 'fun': lambda x: np.sum(x) - 1}, # sum(weights) = 1
            {'type': 'ineq', 'fun': lambda x: portfolio_return_constraint(x, mpt_mean_returns) - target_return} # portfolio_return >= target_return
        ]
        # --- End Constraint Definition ---

        # Minimum Variance optimization with Target Return Constraint
        res_minvar = sco.minimize(portfolio_variance, init_guess, args=(mpt_cov_matrix,),
                                method='SLSQP', bounds=bounds, constraints=constraints_minvar_target,
                                options={'ftol': 1e-9, 'disp': False})

        if res_minvar.success:
            minvar_weights = dict(zip(available_symbols, res_minvar.x))
            # Verify if target return constraint was met (approximately)
            actual_return = portfolio_return_constraint(res_minvar.x, mpt_mean_returns)
            print(f"MinVar w/ Target Return: Achieved Return={actual_return:.8f} (Target >= {target_return:.8f})")
            if actual_return < target_return - 1e-6: # Allow small tolerance
                 print("Warning: Target return constraint might not have been strictly met.")
        else:
             # If SLSQP fails with standard constraint, log it and fallback
             print(f"Min Variance optimization failed (SLSQP, sum(w)=1): {res_minvar.message}")
             minvar_weights = {sym: 1/n for sym in available_symbols} # Fallback
    except Exception as e:
        print(f"Error during Min Variance optimization: {e}")
        minvar_weights = {sym: 1/n for sym in available_symbols} # Fallback
    
    try:
        # Max Sharpe optimization
        # Max Sharpe optimization - Use MPT data
        # Max Sharpe optimization - Use standard constraint
        res_maxsharpe = sco.minimize(neg_sharpe_ratio, init_guess, args=(mpt_mean_returns, mpt_cov_matrix),
                                    method='SLSQP', bounds=bounds, constraints=constraints, # Use standard constraint variable
                                    options={'ftol': 1e-9, 'disp': False})
        if res_maxsharpe.success:
            maxsharpe_weights = dict(zip(available_symbols, res_maxsharpe.x))
    except Exception as e:
        print(f"Error in Max Sharpe optimization: {e}")
        maxsharpe_weights = {sym: 1/n for sym in available_symbols}
    
    try:
        # Max Sortino optimization
        # Max Sortino optimization - Use MPT data
        # Max Sortino optimization - Use standard constraint
        res_maxsortino = sco.minimize(neg_sortino_ratio, init_guess, args=(mpt_mean_returns, mpt_cov_matrix, mpt_returns_df[available_symbols]),
                                    method='SLSQP', bounds=bounds, constraints=constraints, # Use standard constraint variable
                                    options={'ftol': 1e-9, 'disp': False})
        if res_maxsortino.success:
            maxsortino_weights = dict(zip(available_symbols, res_maxsortino.x))
    except Exception as e:
        print(f"Error in Max Sortino optimization: {e}")
        maxsortino_weights = {sym: 1/n for sym in available_symbols}
    
    try:
        # Max Omega optimization
        def safe_omega(w):
            try:
                port_returns = mpt_returns_df[available_symbols].dot(w) # Use MPT returns
                result = -compute_omega_ratio(port_returns, 0.0)
                return result if np.isfinite(result) else 1e6  # Return a large value if result is inf/NaN
            except Exception as e:
                print(f"Error in safe_omega function: {e}")
                return 1e6  # Return a large value on error
        
        # Max Omega optimization - Use standard constraint
        res_maxomega = sco.minimize(safe_omega, init_guess,
                                    method='SLSQP', bounds=bounds, constraints=constraints, # Use standard constraint variable
                                    options={'ftol': 1e-9, 'disp': False})
        if res_maxomega.success:
            maxomega_weights = dict(zip(available_symbols, res_maxomega.x))
    except Exception as e:
        print(f"Error in Max Omega optimization: {e}")
        maxomega_weights = {sym: 1/n for sym in available_symbols}
    
    try:
        # Max Calmar optimization
        def safe_calmar(w):
            try:
                port_returns = mpt_returns_df[available_symbols].dot(w) # Use MPT returns
                result = -compute_calmar_ratio(port_returns)
                return result if np.isfinite(result) else 1e6
            except Exception as e:
                print(f"Error in safe_calmar function: {e}")
                return 1e6
        
        # Max Calmar optimization - Use standard constraint
        res_maxcalmar = sco.minimize(safe_calmar, init_guess,
                                    method='SLSQP', bounds=bounds, constraints=constraints, # Use standard constraint variable
                                    options={'ftol': 1e-9, 'disp': False})
        if res_maxcalmar.success:
            maxcalmar_weights = dict(zip(available_symbols, res_maxcalmar.x))
    except Exception as e:
        print(f"Error in Max Calmar optimization: {e}")
        maxcalmar_weights = {sym: 1/n for sym in available_symbols}
    
    try:
        # Modified Sharpe optimization
        res_modsharpe = sco.minimize(neg_modified_sharpe_ratio, init_guess,
                                    # Modified Sharpe optimization - Use MPT data
                                    args=(mpt_mean_returns, mpt_cov_matrix, mpt_returns_df[available_symbols], 0.0),
                                    method='SLSQP', bounds=bounds, constraints=constraints, # Use standard constraint variable
                                    options={'ftol': 1e-9, 'disp': False})
        if res_modsharpe.success:
            modsharpe_weights = dict(zip(available_symbols, res_modsharpe.x))
    except Exception as e:
        print(f"Error in Modified Sharpe optimization: {e}")
        modsharpe_weights = {sym: 1/n for sym in available_symbols}
    
    # --- Max Return (Direct Calculation) ---
    # Find the index of the asset with the highest mean return
    # --- Max Return (Direct Calculation using MPT data) ---
    try:
        if not mpt_mean_returns.empty:
            max_ret_idx = mpt_mean_returns.idxmax() # Use MPT mean returns
            # Create weights: 1.0 for the highest return asset, 0 for others
            maxret_weights_array = np.zeros(n)
            symbol_to_index = {symbol: i for i, symbol in enumerate(available_symbols)}
            if max_ret_idx in symbol_to_index:
                 idx_in_array = symbol_to_index[max_ret_idx]
                 maxret_weights_array[idx_in_array] = 1.0
                 maxret_weights = dict(zip(available_symbols, maxret_weights_array))
            else:
                 # Fallback if symbol not found (shouldn't happen)
                 print(f"Warning: Max return symbol '{max_ret_idx}' not found in available symbols list.")
                 maxret_weights = {sym: 1/n for sym in available_symbols}
        else:
             # Fallback if mpt_mean_returns is empty
             maxret_weights = {sym: 1/n for sym in available_symbols}
    except Exception as e:
        print(f"Error in direct Max Return calculation: {e}")
        maxret_weights = {sym: 1/n for sym in available_symbols} # Fallback on any error

    # Compute portfolio returns using the normalized allocations (not scaled)
    # Use the 120-hour data (returns_df) for the main cumulative chart
    weight_series = pd.Series({sym: normalized_allocations[sym] for sym in available_symbols}) # Use normalized, not scaled
    portfolio_returns = returns_df[available_symbols].multiply(weight_series, axis=1).sum(axis=1)
    portfolio_cum_returns = portfolio_returns.cumsum() # This is for the 120h chart

    # Calculate maximum up to each point and retracement as percentage
    running_maximum = portfolio_cum_returns.expanding().max()
    retracement = ((running_maximum - portfolio_cum_returns) / running_maximum * 100).clip(lower=0)

    # Also compute portfolio returns based on MPT (today's) data for collinearity check
    mpt_portfolio_returns = mpt_returns_df[available_symbols].multiply(weight_series, axis=1).sum(axis=1)
    
    # Create a custom portfolio object from the allocation string for detailed metrics
    custom_portfolio = {
        "combo": [],
        "weights": []
    }

    # Parse allocation string to build portfolio object
    for part in allocation_string.split(','):
        if ':' not in part:
            continue
            
        symbol, weight = part.split(':')
        symbol = symbol.strip()
        if symbol.endswith('.'):  # Remove trailing dot for locked symbols
            symbol = symbol[:-1]
        
        # Store the symbol without removing any prefix
        custom_portfolio["combo"].append(symbol)
        
        # Store the weight as a float value
        try:
            weight_value = float(weight.strip())
            custom_portfolio["weights"].append(weight_value)
        except ValueError:
            continue

    # Create subplots with secondary y-axis enabled
    fig = make_subplots(rows=1, cols=1, shared_xaxes=True,
                        row_heights=[1.0], vertical_spacing=0.03,
                        specs=[[{"secondary_y": True}]],
                        subplot_titles=["Portfolio Cumulative Returns"])
    
    # Add the portfolio trace on the primary y-axis
    fig.add_trace(go.Scatter(
        x=portfolio_cum_returns.index,
        y=portfolio_cum_returns,
        mode='lines',
        name="Portfolio"
    ), secondary_y=False)

    # Store metrics data but don't add annotation yet
    metrics_data = None
    try:
        # Calculate metrics using full historical data
        metrics_data = process_custom_portfolio(custom_portfolio, returns_df)
        
        # Also calculate metrics using only today's data
        today_metrics = process_custom_portfolio(custom_portfolio, returns_df, current_day_only=True)
        
        # Add today's metrics to the annotation
        metrics_text = (
            f"<b>Portfolio Metrics (All Data):</b><br>"
            f"Return: {metrics_data.get('return', 0):.4f}<br>"
            f"Risk: {metrics_data.get('risk', 0):.4f}<br>"
            f"Sharpe: {metrics_data.get('sharpe', 0):.4f}<br>"
            f"Mod Sharpe: {metrics_data.get('mod_sharpe', 0):.4f}<br>"
            f"Sortino: {metrics_data.get('sortino', 0):.4f}<br>"
            f"Omega: {metrics_data.get('omega', 0):.4f}<br>"
            f"Calmar: {metrics_data.get('calmar', 0):.4f}<br>"
            f"<b>Today's Metrics:</b><br>"
            f"Today Return: {today_metrics.get('return', 0):.4f}<br>"
            f"Today Risk: {today_metrics.get('risk', 0):.4f}<br>"
            f"Today Sharpe: {today_metrics.get('sharpe', 0):.4f}<br>"
            f"Pairs: {len(custom_portfolio['combo'])}"
        )
    except Exception as e:
        print(f"Error calculating portfolio metrics: {e}")

    # Check for near-collinearity using MPT data (today's cov matrix)
    cond_number = np.linalg.cond(mpt_cov_matrix)
    suggestion_text = ""
    print("Covariance Matrix Condition Number:", cond_number)  # Debug print
    # Lowered threshold to 20 to help trigger candidate suggestions.
    if cond_number > 10:
        candidate_symbols_global = [
            "EURUSD", "EURGBP", "EURAUD", "EURNZD", "EURCHF", "EURCAD",
            "GBPUSD", "GBPAUD", "GBPNZD", "GBPCHF", "GBPCAD",
            "AUDUSD", "AUDNZD", "AUDCHF", "AUDCAD",
            "NZDUSD", "NZDCHF", "NZDCAD",
            "USDCHF", "USDCAD", "CADCHF",
            "EURJPY", "GBPJPY", "AUDJPY", "NZDJPY", "CADJPY", "CHFJPY", "USDJPY"
            #"XAUUSD"
        ]
        candidate_pool = [s for s in candidate_symbols_global if s not in available_symbols]
        candidate_correlations = {}
        for cand in candidate_pool:
            # Fetch candidate data using today's timeframe for consistency
            # Fetch candidate data using the same MPT timeframe (Today or Friday)
            candidate_data = fetch_data([cand], timeframe=mt5.TIMEFRAME_M1, shift=shift, start_time=mpt_start_time, end_time=mpt_end_time)
            candidate_returns_df = calculate_returns(candidate_data)
            if cand in candidate_returns_df.columns and not mpt_portfolio_returns.empty:
                 # Correlate candidate's daily returns with portfolio's daily returns
                corr_value = candidate_returns_df[cand].corr(mpt_portfolio_returns)
                candidate_correlations[cand] = corr_value  # store the actual correlation value
        sorted_candidates = sorted(candidate_correlations.items(), key=lambda x: abs(x[1]))
        suggestions = [('-' + cand if corr < 0 else cand) for cand, corr in sorted_candidates[:3]]
        if suggestions:
            suggestion_text = "<br><b>Suggested additional pairs to reduce collinearity:</b><br>" + ", ".join(suggestions)

    # Create the weights table
    weights_table = build_weights_table(
        minvar_weights,
        maxsharpe_weights,
        maxsortino_weights,
        maxomega_weights,
        maxcalmar_weights,
        modsharpe_weights,
        maxret_weights,
        suggestion_text
        )
    current_value = portfolio_cum_returns.iloc[-1]
    fig.add_shape(
        type="line",
        x0=portfolio_cum_returns.index[0],
        y0=current_value,
        x1=portfolio_cum_returns.index[-1],
        y1=current_value,
        line=dict(color="red", dash="dash", width=2),
        xref="x",
        yref="y"
    )
    # Add an annotation showing the last y-axis value at the right-end of the line.
    fig.add_annotation(
        x=portfolio_cum_returns.index[-1],
        y=current_value,
        xref="x",
        yref="y",
        text=f"{current_value:.4f}",
        xanchor="left",
        yanchor="bottom",
        showarrow=False,
        font=dict(color="red", size=12),
        bgcolor="black"
    )
    
    # [After adding the Portfolio trace]
    # Compute the regression channel on the last 1/4th of the presented returns chart.
    filtered_basket = portfolio_cum_returns.dropna()
    N = len(filtered_basket)
    M = max(2, N // 4)  # Use last 1/4th of available data, ensuring at least 2 points.
    if M > 1:  # need at least two data points for a regression
        x = np.arange(M)
        y_subset = filtered_basket.values[-M:]
        reg_index = filtered_basket.index[-M:]
        slope, intercept = np.polyfit(x, y_subset, 1)
        reg_line = slope * x + intercept
        residuals = y_subset - reg_line
        lower_offset = np.min(residuals)
        upper_offset = np.max(residuals)
        lower_channel = reg_line + lower_offset
        upper_channel = reg_line + upper_offset
        # Add the regression channel traces spanning the selected quarter of data.
        fig.add_trace(go.Scatter(
            x=reg_index,
            y=reg_line,
            mode='lines',
            name='Regression Channel (1/4 Data)',
            line=dict(color='yellow', width=3)
        ), secondary_y=False)
        
        fig.add_trace(go.Scatter(
            x=reg_index,
            y=lower_channel,
            mode='lines',
            name='Channel Lower (1/4 Data)',
            line=dict(color='yellow', width=3, dash='dot')
        ), secondary_y=False)
        
        fig.add_trace(go.Scatter(
            x=reg_index,
            y=upper_channel,
            mode='lines',
            name='Channel Upper (1/4 Data)',
            line=dict(color='yellow', width=3, dash='dot')
        ), secondary_y=False)
    else:
        print("Not enough valid basket return data for regression channel.")

    # Add M1 Linear Regression Channel in Blue (from start of day)
    try:
        # Use the existing M1 portfolio cumulative returns data
        # Calculate linear regression channel from start of day
        lr_filtered = portfolio_cum_returns.dropna()
        print(f"M1 data points available for LR channel: {len(lr_filtered)}")

        if len(lr_filtered) >= 10:  # Minimum 10 periods for meaningful regression
            # Use all available data from start of day
            lr_periods = len(lr_filtered)
            lr_x = np.arange(lr_periods)
            lr_y_subset = lr_filtered.values
            lr_reg_index = lr_filtered.index

            # Calculate linear regression on all data from start of day
            lr_slope, lr_intercept = np.polyfit(lr_x, lr_y_subset, 1)
            lr_reg_line = lr_slope * lr_x + lr_intercept
            lr_residuals = lr_y_subset - lr_reg_line
            lr_std_dev = np.std(lr_residuals)
            lr_channel_offset = 2 * lr_std_dev
            lr_lower_channel = lr_reg_line - lr_channel_offset
            lr_upper_channel = lr_reg_line + lr_channel_offset

            # Add M1 regression channel traces in blue
            fig.add_trace(go.Scatter(
                x=lr_reg_index,
                y=lr_reg_line,
                mode='lines',
                name=f'M1 LR Channel ({lr_periods}p)',
                line=dict(color='blue', width=2)
            ), secondary_y=False)

            fig.add_trace(go.Scatter(
                x=lr_reg_index,
                y=lr_lower_channel,
                mode='lines',
                name=f'M1 LR Lower ({lr_periods}p)',
                line=dict(color='blue', width=1, dash='dot')
            ), secondary_y=False)

            fig.add_trace(go.Scatter(
                x=lr_reg_index,
                y=lr_upper_channel,
                mode='lines',
                name=f'M1 LR Upper ({lr_periods}p)',
                line=dict(color='blue', width=1, dash='dot')
            ), secondary_y=False)

            print(f"Added M1 linear regression channel with {lr_periods} periods from start of day")
        else:
            print(f"Not enough M1 data for regression: {len(lr_filtered)} points available (minimum 10 required)")
    except Exception as e:
        print(f"Error adding M1 linear regression channel: {e}")

    # Calculate a dynamic threshold based on number of symbols 
    # Lower threshold when there are more symbols
    symbol_count = len(scaled_allocations)
    #dynamic_threshold = max(0.01, 0.05 / max(1, symbol_count / 5))
    
    # --- NEW SECOND ANNOTATION: LOT ALLOCATION ---
    # Use the scaled allocations (already normalized with total absolute sum = 1 and then scaled)
    # Filter out zero weights with a dynamic threshold
    buys = {sym: scaled_allocations[sym] for sym in scaled_allocations if scaled_allocations[sym] >= 0 and abs(scaled_allocations[sym]) >= 0.01}
    sells = {sym: scaled_allocations[sym] for sym in scaled_allocations if scaled_allocations[sym] < 0 and abs(scaled_allocations[sym]) >= 0.01}
    #buys = {sym: scaled_allocations[sym] for sym in scaled_allocations if scaled_allocations[sym] >= 0 and abs(scaled_allocations[sym]) >= dynamic_threshold}
    #sells = {sym: scaled_allocations[sym] for sym in scaled_allocations if scaled_allocations[sym] < 0 and abs(scaled_allocations[sym]) >= dynamic_threshold}

    # Format the strings for original display (used in annotation)
    buy_strings = [f"{sym}:{alloc:.2f}" for sym, alloc in buys.items()]
    sell_strings = [f"{sym}:{alloc:.2f}" for sym, alloc in sells.items()]

    # Format pairs for tracker_weights according to new specification
    formatted_pairs = []
    for sym, weight in {**buys, **sells}.items():
        # Get first letter of base currency
        base = sym[0]
        # Get first letter of quote currency, use 'F' for CHF
        quote = 'F' if sym[3:6] == 'CHF' else sym[3]
        # Convert weight to percentage and format
        weight_val = int(weight * 100)  # Multiply by 100 and round to integer
        # Add to formatted pairs
        formatted_pairs.append(f"{base}{quote}{weight_val}")
            
    # Create combined string for tracker weights without spaces after commas
    tracker_weights = ",".join(formatted_pairs)

    # Create annotation string with Buy/Sell prefixes
    second_annotation = ""
    if buy_strings:
        second_annotation += "Buy: " + ", ".join(f"{sym}: {alloc:.2f}" for sym, alloc in buys.items())
    if sell_strings:
        if second_annotation:
            second_annotation += "<br>"
        second_annotation += "Sell: " + ", ".join(f"{sym}: {alloc:.2f}" for sym, alloc in sells.items())

    fig.add_annotation(
        xref="paper", yref="paper",
        x=0.2, y=0.99,  # adjust y position as needed
        text=second_annotation,
        showarrow=False,
        font=dict(color="white"),
        bordercolor="white",
        borderwidth=1
    )

    # Add retracement trace
    fig.add_trace(
        go.Scatter(
            x=retracement.index,
            y=retracement,
            mode='lines',
            name='Retracement',
            fill='tozeroy',
            fillcolor='rgba(255, 165, 0, 0.2)',
            line=dict(color='rgba(255, 165, 0, 0.8)', width=1),
            yaxis='y2'
        )
    )

    # Add horizontal lines for retracement levels
    for level, color in [(12.5, 'green'), (25, 'yellow'), (37.5, 'orange'), (50, 'red')]:
        fig.add_trace(
            go.Scatter(
                x=[portfolio_cum_returns.index[0], portfolio_cum_returns.index[-1]],
                y=[level, level],
                mode='lines',
                name=f'{level}% Level',
                line=dict(color=color, width=2, dash='dot'),
                showlegend=False
            ),
            secondary_y=True
        )

    fig.update_layout(
        title="MPT Tracker - Portfolio Cumulative Returns",
        yaxis_title="Cumulative Returns",
        yaxis2=dict(
            title='Drawdown (%)',
            overlaying='y',
            side='right',
            showgrid=False,
            range=[0, 100]  # Show full percentage scale
        ),
        paper_bgcolor='black',
        plot_bgcolor='black',
        font=dict(color='white'),
        height=1250,
        hovermode="x unified",
        margin=dict(r=0),  # Remove right margin to expand chart fully
        legend=dict(
            x=0.05,        # Slightly more spacing from left edge
            y=0.8,         # Position below metrics box
            xanchor='left',
            yanchor='top'
        )
    )

    # Now add the metrics annotation after the figure is created
    if metrics_data and 'today_metrics' in locals():
        metrics_text = (
            f"<b>Today's Metrics:</b><br>"
            f"Today Return: {today_metrics.get('return', 0):.4f}<br>"
            f"Today Risk: {today_metrics.get('risk', 0):.4f}<br>"
            f"Today Sharpe: {today_metrics.get('sharpe', 0):.4f}<br>"
            f"Today Mod Sharpe: {today_metrics.get('mod_sharpe', 0):.4f}<br>"
            f"Today Sortino: {today_metrics.get('sortino', 0):.4f}<br>"
            f"Today Omega: {today_metrics.get('omega', 0):.4f}<br>" 
            f"Today Calmar: {today_metrics.get('calmar', 0):.4f}<br>" 
            f"Pairs: {len(custom_portfolio['combo'])}"
        )
        
        fig.add_annotation(
            x=0.01,
            y=0.99,
            xref="paper",
            yref="paper",
            text=metrics_text,
            showarrow=False,
            font=dict(color="white", size=12),
            bordercolor="white",
            borderwidth=1,
            bgcolor="rgba(0, 0, 0, 0.8)",
            align="left"
        )
        
    fig.update_xaxes(rangebreaks=[dict(bounds=["sat", "mon"])])

    # Store the computed weights for later use.
    store_data = {
        "minvar": minvar_weights, 
        "maxsharpe": maxsharpe_weights,
        "maxsortino": maxsortino_weights,
        "maxomega": maxomega_weights,
        "maxcalmar": maxcalmar_weights,
        "modsharpe": modsharpe_weights,
        "maxret": maxret_weights,
        "locked": list(locked_symbols)  # Save the locked symbol list
    }
    
    # Use the normalized weights for portfolio construction
    portfolio_object = {
        "combo": [],
        "weights": []
    }
    
    # Ensure data integrity when creating portfolio object
    for symbol, weight in normalized_allocations.items():
        # Skip any NaN or infinite weights
        if np.isfinite(weight):
            portfolio_object["combo"].append(symbol)
            portfolio_object["weights"].append(weight)
    
    # Generate the combined MPT string
    combined_mpt_string = create_combined_mpt_string(portfolio_object, [], tracker_weights)

    # Cache the result for weekend use (only during actual trading days)
    # Create the M15 chart with multiple linear regression channels
    # Convert custom_portfolio to simple symbol-to-weight mapping
    portfolio_weights = {symbol: weight for symbol, weight in zip(custom_portfolio["combo"], custom_portfolio["weights"])}
    m15_fig = create_m15_chart_with_lr_channels(portfolio_weights, symbols)

    result_tuple = (weights_table, fig, store_data, combined_mpt_string, m15_fig)

    # Only cache if we're actually on a weekday and not using weekend logic
    if weekday < 5:  # Monday to Friday
        print(f"Caching data for weekend use (weekday {weekday})")
        cache_friday_data(cache_key, result_tuple)
    else:
        print(f"Not caching data (weekend day {weekday})")

    # Return the table, figure, and store data
    return result_tuple